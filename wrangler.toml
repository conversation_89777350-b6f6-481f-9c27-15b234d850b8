name = "college-employment-test-robot"
main = "src/index.ts"
compatibility_date = "2023-12-01"

# 环境变量
[vars]
TARGET_API_BASE = "https://college-employment-survey.aibook2099.workers.dev"
TEST_MODE = "production_testing"
MAX_CONCURRENT_REQUESTS = "50"
DEFAULT_TEST_INTENSITY = "10"
ENVIRONMENT = "isolated_testing"

# KV存储 - 用于存储测试配置和结果
[[kv_namespaces]]
binding = "TEST_CONFIG"
id = "23aa6c1a922143a2be183112a55d3097"

[[kv_namespaces]]
binding = "TEST_RESULTS"
id = "adf50f33b1424464aa857599d5215239"

[[kv_namespaces]]
binding = "TEST_METRICS"
id = "dadcbf1ec08c411a8343cd0463bdfe7b"

[[kv_namespaces]]
binding = "TEST_DATA_POOL"
id = "ddcc6737d57a46b3872719a63828a060"

# D1 数据库 - 用于存储详细的测试数据
[[d1_databases]]
binding = "TEST_DB"
database_name = "test-robot-db"
database_id = "3fcc55db-e357-4f2c-8156-cae83a75e9e6"
