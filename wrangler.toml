name = "college-employment-test-robot"
main = "src/index.ts"
compatibility_date = "2023-12-01"

# 环境变量
[vars]
TARGET_API_BASE = "https://college-employment-survey.aibook2099.workers.dev"
TEST_MODE = "development"
MAX_CONCURRENT_REQUESTS = "50"
DEFAULT_TEST_INTENSITY = "10"

# 定时任务配置 - 每分钟执行一次
[[triggers.crons]]
cron = "* * * * *"

# KV存储 - 用于存储测试配置和结果
[[kv_namespaces]]
binding = "TEST_CONFIG"
id = "test_config_namespace"
preview_id = "test_config_preview"

[[kv_namespaces]]
binding = "TEST_RESULTS"
id = "test_results_namespace"
preview_id = "test_results_preview"

[[kv_namespaces]]
binding = "TEST_METRICS"
id = "test_metrics_namespace"
preview_id = "test_metrics_preview"

[[kv_namespaces]]
binding = "TEST_DATA_POOL"
id = "test_data_pool_namespace"
preview_id = "test_data_pool_preview"

# D1 数据库 - 用于存储详细的测试数据
[[d1_databases]]
binding = "TEST_DB"
database_name = "test-robot-db"
database_id = "your-d1-database-id"

# 生产环境配置
[env.production]
name = "college-employment-test-robot-prod"
vars = { 
  TARGET_API_BASE = "https://college-employment-survey.aibook2099.workers.dev", 
  TEST_MODE = "production",
  MAX_CONCURRENT_REQUESTS = "30",
  DEFAULT_TEST_INTENSITY = "5"
}

# 预发环境配置
[env.staging]
name = "college-employment-test-robot-staging"
vars = { 
  TARGET_API_BASE = "https://college-employment-survey.aibook2099.workers.dev", 
  TEST_MODE = "staging",
  MAX_CONCURRENT_REQUESTS = "20",
  DEFAULT_TEST_INTENSITY = "3"
}

# 开发环境配置
[env.development]
name = "college-employment-test-robot-dev"
vars = { 
  TARGET_API_BASE = "https://college-employment-survey.aibook2099.workers.dev", 
  TEST_MODE = "development",
  MAX_CONCURRENT_REQUESTS = "100",
  DEFAULT_TEST_INTENSITY = "20"
}
