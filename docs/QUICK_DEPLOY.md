# 测试机器人快速部署指南

## 🚀 快速开始

### 1. 环境准备
```bash
# 确保在测试机器人目录中
cd college-employment-test-robot

# 安装依赖
npm install

# 验证本地运行
npx wrangler dev
```

### 2. 创建 Cloudflare 资源

#### 创建 KV 命名空间
```bash
# 创建测试配置存储
npx wrangler kv:namespace create "TEST_CONFIG"

# 创建测试结果存储
npx wrangler kv:namespace create "TEST_RESULTS"

# 创建测试指标存储
npx wrangler kv:namespace create "TEST_METRICS"

# 创建测试数据池存储
npx wrangler kv:namespace create "TEST_DATA_POOL"
```

#### 创建 D1 数据库
```bash
# 创建测试数据库
npx wrangler d1 create test-robot-db
```

### 3. 更新配置文件

将创建的资源ID更新到 `wrangler.toml`:

```toml
name = "college-employment-test-robot"
main = "src/index.ts"
compatibility_date = "2023-12-01"

[vars]
TARGET_API_BASE = "https://college-employment-survey.aibook2099.workers.dev"
TEST_MODE = "development"
MAX_CONCURRENT_REQUESTS = "50"
DEFAULT_TEST_INTENSITY = "10"

[[triggers.crons]]
cron = "* * * * *"

[[kv_namespaces]]
binding = "TEST_CONFIG"
id = "your-test-config-kv-id"
preview_id = "your-test-config-preview-id"

[[kv_namespaces]]
binding = "TEST_RESULTS"
id = "your-test-results-kv-id"
preview_id = "your-test-results-preview-id"

[[kv_namespaces]]
binding = "TEST_METRICS"
id = "your-test-metrics-kv-id"
preview_id = "your-test-metrics-preview-id"

[[kv_namespaces]]
binding = "TEST_DATA_POOL"
id = "your-test-data-pool-kv-id"
preview_id = "your-test-data-pool-preview-id"

[[d1_databases]]
binding = "TEST_DB"
database_name = "test-robot-db"
database_id = "your-d1-database-id"
```

### 4. 部署到 Cloudflare

```bash
# 部署到开发环境
npx wrangler deploy

# 或部署到生产环境
npx wrangler deploy --env production
```

### 5. 初始化数据池

```bash
# 获取部署后的URL
ROBOT_URL="https://college-employment-test-robot.your-account.workers.dev"

# 初始化数据池
curl -X POST $ROBOT_URL/api/data/init-pools

# 检查状态
curl $ROBOT_URL/api/status
```

### 6. 启动测试

```bash
# 启动测试机器人
curl -X POST $ROBOT_URL/api/start

# 检查运行状态
curl $ROBOT_URL/api/status
```

## 🔧 配置选项

### 测试强度配置
```json
{
  "enabled": true,
  "intensity": {
    "questionnaire": 10,  // 每分钟问卷数
    "story": 5,          // 每分钟故事数
    "registration": 2    // 每分钟注册数
  },
  "contentMix": {
    "normal": 0.8,       // 正常内容比例
    "sensitive": 0.15,   // 敏感内容比例
    "violation": 0.05    // 违规内容比例
  },
  "schedule": {
    "activeHours": ["09:00-12:00", "14:00-18:00"],
    "intervalMinutes": 5,
    "weekendEnabled": true
  }
}
```

### 更新配置
```bash
curl -X POST $ROBOT_URL/api/config \
  -H "Content-Type: application/json" \
  -d '{"enabled":true,"intensity":{"questionnaire":20,"story":10,"registration":5}}'
```

## 📊 监控和管理

### 查看状态
```bash
# 系统状态
curl $ROBOT_URL/api/status

# 性能指标
curl $ROBOT_URL/api/metrics

# 数据池统计
curl $ROBOT_URL/api/data/pools
```

### 手动触发测试
```bash
# 触发问卷测试
curl -X POST $ROBOT_URL/api/trigger/questionnaire \
  -H "Content-Type: application/json" \
  -d '{"count": 5}'

# 触发故事测试
curl -X POST $ROBOT_URL/api/trigger/story \
  -H "Content-Type: application/json" \
  -d '{"count": 3}'
```

### 停止测试
```bash
curl -X POST $ROBOT_URL/api/stop
```

## 🛡️ 安全注意事项

### 1. 环境隔离
- 确保测试机器人部署在独立的 Cloudflare 项目中
- 使用不同的域名和资源，避免与生产环境混淆

### 2. 测试标识
- 所有测试请求都包含明确的测试标识头
- 生产系统应能识别并适当处理测试请求

### 3. 数据清理
- 定期清理测试数据，避免污染生产数据
- 设置合理的数据过期时间

### 4. 访问控制
- 限制测试机器人的访问权限
- 监控测试活动，防止滥用

## 🔍 故障排除

### 常见问题

1. **KV 存储错误**
   ```
   Error: Cannot read properties of undefined (reading 'put')
   ```
   解决: 检查 wrangler.toml 中的 KV 配置是否正确

2. **定时任务不触发**
   ```
   Warning: Miniflare does not currently trigger scheduled Workers automatically
   ```
   解决: 这是本地开发的正常提示，部署后会自动触发

3. **API 请求失败**
   ```
   Error: fetch failed
   ```
   解决: 检查目标 API 地址是否正确，网络是否可达

### 调试命令
```bash
# 查看日志
npx wrangler tail

# 检查配置
npx wrangler whoami
npx wrangler kv:namespace list

# 测试连接
curl -v $ROBOT_URL/
```

## 📈 性能优化

### 1. 调整并发数
根据目标系统的承载能力调整 `MAX_CONCURRENT_REQUESTS`

### 2. 优化测试间隔
根据实际需求调整 `intervalMinutes` 和测试强度

### 3. 监控资源使用
定期检查 KV 存储和 D1 数据库的使用情况

## 📞 支持

如果遇到问题，请：
1. 检查本文档的故障排除部分
2. 查看 Cloudflare Workers 文档
3. 提交 GitHub Issue

---

**部署完成后，测试机器人将自动开始工作，为您的问卷系统提供持续的性能和功能验证！**
