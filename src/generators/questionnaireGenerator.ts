/**
 * 问卷数据生成器
 * 生成符合真实场景的问卷测试数据
 */

import { Env } from '../index';
import { TestConfig, TestContentCombination, VariablePool } from '../types';

export class QuestionnaireGenerator {
  private env: Env;
  private config: TestConfig;

  constructor(env: Env, config: TestConfig) {
    this.env = env;
    this.config = config;
  }

  /**
   * 从测试组合生成问卷数据
   */
  async generateFromCombination(combination: TestContentCombination): Promise<any> {
    try {
      // 获取变量池
      const variablesStr = await this.env.TEST_DATA_POOL.get('variables');
      const variables: VariablePool = variablesStr ? JSON.parse(variablesStr) : this.getDefaultVariables();

      // 生成基础问卷数据
      const questionnaireData = {
        // 用户信息
        userInfo: combination.user.profile,
        
        // 问卷回答
        answers: {
          step1: this.generateStep1(variables),
          step2: this.generateStep2(variables),
          step3: this.generateStep3(variables),
          step4: this.generateStep4(variables),
          step5: this.generateStep5(variables),
          step6: this.generateStep6(combination, variables) // 个人心声，重点测试审核
        },

        // 测试标记
        _testMeta: combination.testMeta
      };

      return questionnaireData;
    } catch (error) {
      console.error('生成问卷数据失败:', error);
      throw error;
    }
  }

  /**
   * 第一步：职业方向
   */
  private generateStep1(variables: VariablePool): any {
    const careerDirections = ['技术研发', '产品管理', '数据分析', '人工智能', '网络安全'];
    const interests = ['技术创新', '团队协作', '解决问题', '学习成长', '稳定发展'];
    const workEnvironments = ['大公司', '创业公司', '外企', '国企', '自主创业'];

    return {
      careerDirection: this.randomChoice(careerDirections),
      interest: this.randomChoice(interests),
      workEnvironment: this.randomChoice(workEnvironments),
      location: this.randomChoice(variables.locations)
    };
  }

  /**
   * 第二步：技能评估
   */
  private generateStep2(variables: VariablePool): any {
    const selectedSkills = this.randomSample(variables.skills, this.randomInt(3, 5));
    const skillLevels = {};
    
    for (const skill of selectedSkills) {
      skillLevels[skill] = this.randomChoice(['初级', '中级', '高级', '专家']);
    }

    return {
      technicalSkills: skillLevels,
      softSkills: {
        communication: this.randomInt(1, 5),
        teamwork: this.randomInt(1, 5),
        leadership: this.randomInt(1, 5),
        problemSolving: this.randomInt(1, 5),
        learning: this.randomInt(1, 5)
      },
      certifications: this.randomSample([
        'CET-4', 'CET-6', '计算机二级', '软考中级'
      ], this.randomInt(0, 3))
    };
  }

  /**
   * 第三步：学习偏好
   */
  private generateStep3(variables: VariablePool): any {
    return {
      learningStyle: this.randomChoice(['视觉学习', '听觉学习', '动手实践', '理论研究']),
      preferredResources: this.randomSample([
        '在线课程', '技术书籍', '实战项目', '技术博客', '视频教程'
      ], this.randomInt(2, 4)),
      timeInvestment: this.randomChoice(['每天1-2小时', '每天2-4小时', '周末集中学习']),
      goals: this.randomSample([
        '提升技术能力', '获得认证', '找到实习', '准备求职'
      ], this.randomInt(1, 3))
    };
  }

  /**
   * 第四步：实习经历
   */
  private generateStep4(variables: VariablePool): any {
    const hasInternship = Math.random() > 0.3;
    
    if (!hasInternship) {
      return {
        hasExperience: false,
        reason: this.randomChoice(['学业繁忙', '没有合适机会', '准备不足', '家庭原因'])
      };
    }

    return {
      hasExperience: true,
      experiences: [{
        company: '某科技公司',
        position: this.randomChoice(['前端开发实习生', '后端开发实习生', '产品实习生']),
        duration: this.randomChoice(['1个月', '2个月', '3个月']),
        skills: this.randomSample(variables.skills, this.randomInt(2, 4)),
        evaluation: this.randomChoice(['优秀', '良好', '合格'])
      }]
    };
  }

  /**
   * 第五步：职业规划
   */
  private generateStep5(variables: VariablePool): any {
    return {
      shortTermGoals: this.randomSample([
        '完成学业', '找到实习', '提升技能', '准备求职'
      ], this.randomInt(1, 3)),
      longTermGoals: this.randomSample([
        '成为技术专家', '担任管理职位', '自主创业'
      ], this.randomInt(1, 2)),
      expectedSalary: this.randomChoice(['5-8K', '8-12K', '12-18K', '18-25K']),
      workLifeBalance: this.randomInt(1, 5),
      willingnessToTravel: this.randomChoice(['完全接受', '偶尔可以', '尽量避免'])
    };
  }

  /**
   * 第六步：个人心声 (重点测试审核)
   */
  private generateStep6(combination: TestContentCombination, variables: VariablePool): any {
    let personalThoughts: string;
    let careerConcerns: string;
    let suggestions: string;

    if (combination.voice) {
      // 使用数据池中的内容
      personalThoughts = this.fillTemplate(combination.voice.template, variables, combination.voice.category);
      careerConcerns = this.generateConcerns(combination.voice.category, variables);
      suggestions = this.generateSuggestions(combination.voice.category, variables);
    } else {
      // 生成默认正常内容
      personalThoughts = this.generateNormalThoughts(variables);
      careerConcerns = this.generateNormalConcerns(variables);
      suggestions = this.generateNormalSuggestions(variables);
    }

    return {
      personalThoughts,
      careerConcerns,
      suggestions,
      additionalComments: this.generateAdditionalComments()
    };
  }

  /**
   * 填充模板
   */
  private fillTemplate(template: string, variables: VariablePool, category: string): string {
    let content = template;
    
    // 替换变量
    content = content.replace(/{grade}/g, this.randomChoice(variables.grades));
    content = content.replace(/{major}/g, this.randomChoice(variables.majors));
    content = content.replace(/{skill}/g, this.randomChoice(variables.skills));
    content = content.replace(/{emotion}/g, 
      category === 'sensitive' ? 
        this.randomChoice(variables.sensitiveEmotions) : 
        this.randomChoice(variables.emotions)
    );
    content = content.replace(/{worry}/g, this.randomChoice(variables.concerns));
    content = content.replace(/{condition}/g, this.randomChoice(['不太好', '有限', '困难']));
    
    return content;
  }

  /**
   * 生成正常内容的个人想法
   */
  private generateNormalThoughts(variables: VariablePool): string {
    const templates = [
      `我对自己的专业很感兴趣，希望能在这个领域深入发展。`,
      `通过大学的学习，我发现了自己的兴趣所在，也明确了未来的方向。`,
      `我认为持续学习和实践是成功的关键，会努力提升自己的能力。`
    ];
    
    return this.randomChoice(templates);
  }

  /**
   * 生成担忧
   */
  private generateConcerns(category: string, variables: VariablePool): string {
    if (category === 'sensitive') {
      return this.randomChoice([
        '担心毕业后找不到理想的工作，就业压力很大。',
        '家庭经济条件有限，希望能尽快经济独立。',
        '对自己的能力缺乏信心，担心无法胜任工作。'
      ]);
    }
    
    return this.randomChoice([
      '担心自己的技能还不够扎实，需要更多的实践经验。',
      '希望能找到合适的导师或前辈给予指导。',
      '对行业发展趋势还需要更深入的了解。'
    ]);
  }

  /**
   * 生成建议
   */
  private generateSuggestions(category: string, variables: VariablePool): string {
    if (category === 'violation') {
      return this.randomChoice([
        '这种调查就应该取消，完全是浪费时间。',
        '学校应该把精力放在真正有用的事情上。'
      ]);
    }
    
    return this.randomChoice(variables.suggestions);
  }

  /**
   * 生成正常建议
   */
  private generateNormalSuggestions(variables: VariablePool): string {
    return this.randomChoice(variables.suggestions);
  }

  /**
   * 生成正常担忧
   */
  private generateNormalConcerns(variables: VariablePool): string {
    return this.randomChoice([
      '担心自己的技能还不够扎实，需要更多的实践经验。',
      '希望能找到合适的导师或前辈给予指导。'
    ]);
  }

  /**
   * 生成额外评论
   */
  private generateAdditionalComments(): string {
    if (Math.random() > 0.7) {
      return this.randomChoice([
        '感谢提供这次调查机会，希望能对改善教育质量有所帮助。',
        '希望学校能理解学生的需求，提供更多支持。',
        ''
      ]);
    }
    return '';
  }

  /**
   * 获取默认变量
   */
  private getDefaultVariables(): VariablePool {
    return {
      grades: ['大一', '大二', '大三', '大四'],
      majors: ['计算机科学', '软件工程', '数据科学'],
      schools: ['清华大学', '北京大学', '复旦大学'],
      locations: ['北京', '上海', '深圳', '杭州'],
      skills: ['编程', '数据分析', '项目管理'],
      emotions: ['有点担心', '压力不大', '比较乐观'],
      concerns: ['技能不足', '经验缺乏', '竞争激烈'],
      suggestions: ['增加实践机会', '提供就业指导', '改善教学质量'],
      sensitiveEmotions: ['很焦虑', '非常担心', '压力很大'],
      sensitiveTopics: ['就业压力', '经济困难', '能力不足'],
      violationWords: ['垃圾', '废物', '骗子'],
      violationPhrases: ['完全没用', '纯属扯淡', '应该关闭']
    };
  }

  // 辅助方法
  private randomChoice<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }

  private randomInt(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  private randomSample<T>(array: T[], count: number): T[] {
    const shuffled = [...array].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, array.length));
  }
}
