/**
 * 故事数据生成器
 * 生成符合真实场景的故事测试数据
 */

import { Env } from '../index';
import { TestConfig, TestContentCombination, VariablePool } from '../types';

export class StoryGenerator {
  private env: Env;
  private config: TestConfig;

  constructor(env: Env, config: TestConfig) {
    this.env = env;
    this.config = config;
  }

  /**
   * 从测试组合生成故事数据
   */
  async generateFromCombination(combination: TestContentCombination): Promise<any> {
    try {
      // 获取变量池
      const variablesStr = await this.env.TEST_DATA_POOL.get('variables');
      const variables: VariablePool = variablesStr ? JSON.parse(variablesStr) : this.getDefaultVariables();

      let title: string;
      let content: string;
      let tags: string[];

      if (combination.story) {
        // 使用数据池中的故事
        title = this.fillTemplate(combination.story.title, variables, combination.story.category);
        content = this.fillTemplate(combination.story.content, variables, combination.story.category);
        tags = combination.story.tags;
      } else {
        // 生成默认正常故事
        title = '我的学习经历分享';
        content = this.generateNormalStoryContent(variables);
        tags = ['学习', '分享', '经验'];
      }

      const storyData = {
        // 用户信息
        userId: combination.user.uuid,
        userProfile: combination.user.profile,
        
        // 故事内容
        title,
        content,
        tags,
        
        // 故事元数据
        category: combination.story?.category || 'normal',
        wordCount: content.length,
        estimatedReadTime: Math.ceil(content.length / 200), // 假设每分钟200字
        
        // 测试标记
        _testMeta: combination.testMeta
      };

      return storyData;
    } catch (error) {
      console.error('生成故事数据失败:', error);
      throw error;
    }
  }

  /**
   * 填充模板
   */
  private fillTemplate(template: string, variables: VariablePool, category: string): string {
    let content = template;
    
    // 替换变量
    content = content.replace(/{field}/g, this.randomChoice(['计算机', '软件工程', '数据科学']));
    content = content.replace(/{major}/g, this.randomChoice(variables.majors));
    content = content.replace(/{company}/g, '某知名公司');
    content = content.replace(/{duration}/g, this.randomChoice(['三个月', '半年', '一年']));
    content = content.replace(/{skill}/g, this.randomChoice(variables.skills));
    content = content.replace(/{challenge}/g, this.randomChoice(['遇到困难', '感到迷茫', '压力很大']));
    content = content.replace(/{method}/g, this.randomChoice(['努力学习', '请教前辈', '不断练习']));
    content = content.replace(/{insight}/g, this.randomChoice(['实践的重要性', '团队合作的价值', '持续学习的必要性']));
    content = content.replace(/{guidance}/g, this.randomChoice(['明确的方向', '宝贵的经验', '重要的启发']));
    
    // 违规内容变量
    if (category === 'violation') {
      content = content.replace(/{negative_word}/g, this.randomChoice(variables.violationWords));
      content = content.replace(/{insult}/g, this.randomChoice(['废物', '骗子', '垃圾']));
      content = content.replace(/{complaint}/g, this.randomChoice(['太差了', '完全不行', '毫无用处']));
      content = content.replace(/{threat}/g, this.randomChoice(['举报', '投诉', '抵制']));
      content = content.replace(/{exploitation}/g, this.randomChoice(['压榨', '剥削', '欺骗']));
      content = content.replace(/{negative_view}/g, this.randomChoice(['没希望', '太黑暗', '完全腐败']));
    }
    
    return content;
  }

  /**
   * 生成正常故事内容
   */
  private generateNormalStoryContent(variables: VariablePool): string {
    const templates = [
      `作为一名${this.randomChoice(variables.majors)}专业的学生，我想分享一下我的学习经历。在大学期间，我主要学习了${this.randomChoice(variables.skills)}相关的知识。虽然刚开始觉得有些困难，但通过不断努力和实践，我逐渐掌握了这些技能。这个过程让我明白了持续学习的重要性，也为我的未来发展奠定了基础。`,
      
      `最近完成了一个关于${this.randomChoice(variables.skills)}的项目，想和大家分享一下经验。这个项目让我学到了很多实用的技能，也让我对${this.randomChoice(['技术发展', '行业趋势', '职业规划'])}有了更深的理解。希望我的经历能对其他同学有所帮助。`,
      
      `在${this.randomChoice(variables.locations)}实习的经历让我收获很多。公司的工作环境很好，同事们也很友善。通过这次实习，我不仅提升了专业技能，还学会了如何与团队协作。这段经历对我的职业发展很有帮助。`
    ];
    
    return this.randomChoice(templates);
  }

  /**
   * 获取默认变量
   */
  private getDefaultVariables(): VariablePool {
    return {
      grades: ['大一', '大二', '大三', '大四'],
      majors: ['计算机科学', '软件工程', '数据科学'],
      schools: ['清华大学', '北京大学', '复旦大学'],
      locations: ['北京', '上海', '深圳', '杭州'],
      skills: ['编程', '数据分析', '项目管理'],
      emotions: ['有点担心', '压力不大', '比较乐观'],
      concerns: ['技能不足', '经验缺乏', '竞争激烈'],
      suggestions: ['增加实践机会', '提供就业指导', '改善教学质量'],
      sensitiveEmotions: ['很焦虑', '非常担心', '压力很大'],
      sensitiveTopics: ['就业压力', '经济困难', '能力不足'],
      violationWords: ['垃圾', '废物', '骗子'],
      violationPhrases: ['完全没用', '纯属扯淡', '应该关闭']
    };
  }

  // 辅助方法
  private randomChoice<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }
}
