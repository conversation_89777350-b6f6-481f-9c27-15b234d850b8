/**
 * 用户数据生成器
 * 生成A+B半匿名用户注册数据
 */

import { Env } from '../index';
import { TestConfig, TestContentCombination } from '../types';

export class UserGenerator {
  private env: Env;
  private config: TestConfig;

  constructor(env: Env, config: TestConfig) {
    this.env = env;
    this.config = config;
  }

  /**
   * 从测试组合生成用户注册数据
   */
  async generateFromCombination(combination: TestContentCombination): Promise<any> {
    try {
      const userData = {
        // A+B半匿名注册数据
        uuid: combination.user.uuid,
        profile: combination.user.profile,
        
        // 注册信息
        registrationType: 'semi_anonymous',
        registrationMethod: 'A+B',
        
        // 用户偏好设置
        preferences: {
          notifications: this.randomChoice([true, false]),
          privacy: this.randomChoice(['public', 'private', 'friends']),
          language: 'zh-CN'
        },
        
        // 注册时间和来源
        registrationTime: new Date().toISOString(),
        source: 'test_robot',
        userAgent: 'TestRobot/1.0',
        
        // 测试标记
        _testMeta: combination.testMeta
      };

      return userData;
    } catch (error) {
      console.error('生成用户数据失败:', error);
      throw error;
    }
  }

  // 辅助方法
  private randomChoice<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }
}
