/**
 * 测试机器人类型定义
 */

// 测试配置
export interface TestConfig {
  enabled: boolean;
  intensity: {
    questionnaire: number; // 每分钟问卷提交数
    story: number;         // 每分钟故事提交数
    registration: number;  // 每分钟用户注册数
  };
  contentMix: {
    normal: number;     // 正常内容比例 (0-1)
    sensitive: number;  // 敏感内容比例 (0-1)
    violation: number;  // 违规内容比例 (0-1)
  };
  schedule: {
    activeHours: string[];  // 活跃时间段 ['09:00-12:00', '14:00-18:00']
    timezone: string;       // 时区
    weekendEnabled: boolean; // 是否在周末运行
    intervalMinutes: number; // 发布间隔(分钟) 1, 5, 10, 30, 60
  };
  targets: {
    apiBase: string;        // 目标API地址
    maxConcurrent: number;  // 最大并发数
    timeout: number;        // 请求超时时间(ms)
  };
}

// 任务结果
export interface TaskResult {
  taskType: string;
  success: boolean;
  responseTime: number;
  statusCode?: number;
  error?: string;
  timestamp: string;
  metadata?: any;
}

// 测试数据标识
export interface TestDataIdentifier {
  source: 'test-robot';
  testSessionId: string;
  robotId: string;
  timestamp: string;
  category: 'synthetic';
  autoCleanup: boolean;
  expiresAt: string;
}

// 用户数据池
export interface UserPool {
  id: string;
  uuid: string;
  profile: {
    grade: string;
    major: string;
    gender: string;
    age: number;
    school: string;
    location: string;
  };
  createdAt: string;
  usageCount: number;
}

// 问卷心声数据池
export interface QuestionnaireVoicePool {
  id: string;
  content: string;
  category: 'normal' | 'sensitive' | 'violation';
  template: string;
  variables: Record<string, string>;
  expectedResult: 'approve' | 'reject';
  tags: string[];
  createdAt: string;
  usageCount: number;
}

// 故事数据池
export interface StoryPool {
  id: string;
  title: string;
  content: string;
  category: 'normal' | 'sensitive' | 'violation';
  template: string;
  variables: Record<string, string>;
  expectedResult: 'approve' | 'reject';
  tags: string[];
  createdAt: string;
  usageCount: number;
}

// 测试内容组合
export interface TestContentCombination {
  user: UserPool;
  questionnaire?: any;
  voice?: QuestionnaireVoicePool;
  story?: StoryPool;
  testMeta: TestDataIdentifier;
}

// 性能指标
export interface PerformanceMetrics {
  timestamp: string;
  responseTime: {
    avg: number;
    p50: number;
    p95: number;
    p99: number;
  };
  throughput: {
    requestsPerSecond: number;
    requestsPerMinute: number;
  };
  success: {
    total: number;
    successful: number;
    failed: number;
    successRate: number;
  };
  errors: {
    count: number;
    rate: number;
    types: Record<string, number>;
  };
}

// 监控数据
export interface MonitoringData {
  testMetrics: PerformanceMetrics;
  systemMetrics: {
    cpuUsage: number;
    memoryUsage: number;
    activeConnections: number;
    queueLength: number;
  };
  businessMetrics: {
    reviewAccuracy: number;
    processingTime: number;
    contentDistribution: Record<string, number>;
  };
}

// 测试报告
export interface TestReport {
  summary: {
    testDuration: number;
    totalSubmissions: number;
    successRate: number;
    averageResponseTime: number;
  };
  performance: {
    throughputAnalysis: any;
    latencyAnalysis: any;
    errorAnalysis: any;
  };
  reviewEffectiveness: {
    accuracyByContentType: Record<string, number>;
    falsePositiveRate: number;
    falseNegativeRate: number;
  };
  recommendations: string[];
}

// 内容模板
export interface ContentTemplate {
  id: string;
  type: 'questionnaire_voice' | 'story';
  category: 'normal' | 'sensitive' | 'violation';
  template: string;
  variables: string[];
  tags: string[];
  expectedResult: 'approve' | 'reject';
  weight: number; // 使用权重，用于随机选择
}

// 变量池
export interface VariablePool {
  grades: string[];
  majors: string[];
  schools: string[];
  locations: string[];
  skills: string[];
  emotions: string[];
  concerns: string[];
  suggestions: string[];
  // 敏感内容变量
  sensitiveEmotions: string[];
  sensitiveTopics: string[];
  // 违规内容变量
  violationWords: string[];
  violationPhrases: string[];
}
