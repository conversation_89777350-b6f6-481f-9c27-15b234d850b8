/**
 * 测试控制器
 * 处理测试机器人的API请求
 */

import { Context } from 'hono';
import { Env } from '../index';
import { TestConfig } from '../types';
import { MonitoringService } from '../services/monitoringService';
import { DataPoolService } from '../services/dataPoolService';

export class TestController {
  /**
   * 获取系统状态
   */
  static async getStatus(c: Context<{ Bindings: Env }>) {
    try {
      const config = await getTestConfig(c.env);
      const monitoring = new MonitoringService(c.env);
      const dataPool = new DataPoolService(c.env);
      
      const [recentMetrics, poolStats] = await Promise.all([
        monitoring.getRecentMetrics(5),
        dataPool.getDataPoolStats()
      ]);

      return c.json({
        success: true,
        data: {
          status: config.enabled ? 'running' : 'stopped',
          config: {
            enabled: config.enabled,
            intensity: config.intensity,
            contentMix: config.contentMix,
            targets: config.targets,
            schedule: config.schedule
          },
          dataPool: poolStats,
          recentMetrics,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      return c.json({
        success: false,
        error: `获取状态失败: ${error.message}`
      }, 500);
    }
  }

  /**
   * 获取配置
   */
  static async getConfig(c: Context<{ Bindings: Env }>) {
    try {
      const config = await getTestConfig(c.env);
      
      return c.json({
        success: true,
        data: config
      });
    } catch (error) {
      return c.json({
        success: false,
        error: `获取配置失败: ${error.message}`
      }, 500);
    }
  }

  /**
   * 更新配置
   */
  static async updateConfig(c: Context<{ Bindings: Env }>) {
    try {
      const newConfig = await c.req.json();
      
      // 验证配置
      if (!validateConfig(newConfig)) {
        return c.json({
          success: false,
          error: '配置格式不正确'
        }, 400);
      }

      // 保存配置
      await c.env.TEST_CONFIG.put('current_config', JSON.stringify(newConfig));

      return c.json({
        success: true,
        message: '配置更新成功',
        data: newConfig
      });
    } catch (error) {
      return c.json({
        success: false,
        error: `更新配置失败: ${error.message}`
      }, 500);
    }
  }

  /**
   * 启动测试
   */
  static async startTest(c: Context<{ Bindings: Env }>) {
    try {
      const config = await getTestConfig(c.env);
      config.enabled = true;
      
      await c.env.TEST_CONFIG.put('current_config', JSON.stringify(config));

      // 记录启动事件
      const monitoring = new MonitoringService(c.env);
      await monitoring.logEvent('test_started', {
        timestamp: new Date().toISOString(),
        config: config.intensity
      });

      return c.json({
        success: true,
        message: '测试已启动',
        data: { enabled: true }
      });
    } catch (error) {
      return c.json({
        success: false,
        error: `启动测试失败: ${error.message}`
      }, 500);
    }
  }

  /**
   * 停止测试
   */
  static async stopTest(c: Context<{ Bindings: Env }>) {
    try {
      const config = await getTestConfig(c.env);
      config.enabled = false;
      
      await c.env.TEST_CONFIG.put('current_config', JSON.stringify(config));

      // 记录停止事件
      const monitoring = new MonitoringService(c.env);
      await monitoring.logEvent('test_stopped', {
        timestamp: new Date().toISOString()
      });

      return c.json({
        success: true,
        message: '测试已停止',
        data: { enabled: false }
      });
    } catch (error) {
      return c.json({
        success: false,
        error: `停止测试失败: ${error.message}`
      }, 500);
    }
  }

  /**
   * 获取指标
   */
  static async getMetrics(c: Context<{ Bindings: Env }>) {
    try {
      const { period = '1h' } = c.req.query();
      const monitoring = new MonitoringService(c.env);
      
      const metrics = await monitoring.getMetrics(period);

      return c.json({
        success: true,
        data: metrics
      });
    } catch (error) {
      return c.json({
        success: false,
        error: `获取指标失败: ${error.message}`
      }, 500);
    }
  }

  /**
   * 获取报告
   */
  static async getReports(c: Context<{ Bindings: Env }>) {
    try {
      const { type = 'summary', period = '24h' } = c.req.query();
      const monitoring = new MonitoringService(c.env);
      
      let report;
      switch (type) {
        case 'performance':
          report = await monitoring.getPerformanceReport(period);
          break;
        case 'accuracy':
          report = await monitoring.getAccuracyReport(period);
          break;
        case 'summary':
        default:
          report = await monitoring.getSummaryReport(period);
          break;
      }

      return c.json({
        success: true,
        data: report
      });
    } catch (error) {
      return c.json({
        success: false,
        error: `获取报告失败: ${error.message}`
      }, 500);
    }
  }

  /**
   * 手动触发问卷测试
   */
  static async triggerQuestionnaire(c: Context<{ Bindings: Env }>) {
    try {
      const { count = 1 } = await c.req.json();
      
      // TODO: 实现手动触发逻辑
      
      return c.json({
        success: true,
        message: `已触发 ${count} 个问卷测试`,
        data: { count, type: 'questionnaire' }
      });
    } catch (error) {
      return c.json({
        success: false,
        error: `触发问卷测试失败: ${error.message}`
      }, 500);
    }
  }

  /**
   * 手动触发故事测试
   */
  static async triggerStory(c: Context<{ Bindings: Env }>) {
    try {
      const { count = 1 } = await c.req.json();
      
      return c.json({
        success: true,
        message: `已触发 ${count} 个故事测试`,
        data: { count, type: 'story' }
      });
    } catch (error) {
      return c.json({
        success: false,
        error: `触发故事测试失败: ${error.message}`
      }, 500);
    }
  }

  /**
   * 手动触发注册测试
   */
  static async triggerRegistration(c: Context<{ Bindings: Env }>) {
    try {
      const { count = 1 } = await c.req.json();
      
      return c.json({
        success: true,
        message: `已触发 ${count} 个注册测试`,
        data: { count, type: 'registration' }
      });
    } catch (error) {
      return c.json({
        success: false,
        error: `触发注册测试失败: ${error.message}`
      }, 500);
    }
  }

  /**
   * 初始化数据池
   */
  static async initDataPools(c: Context<{ Bindings: Env }>) {
    try {
      const dataPool = new DataPoolService(c.env);
      await dataPool.initializeDataPools();

      return c.json({
        success: true,
        message: '数据池初始化成功',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      return c.json({
        success: false,
        error: `初始化数据池失败: ${error.message}`
      }, 500);
    }
  }

  /**
   * 获取数据池信息
   */
  static async getDataPools(c: Context<{ Bindings: Env }>) {
    try {
      const dataPool = new DataPoolService(c.env);
      const stats = await dataPool.getDataPoolStats();

      return c.json({
        success: true,
        data: stats
      });
    } catch (error) {
      return c.json({
        success: false,
        error: `获取数据池信息失败: ${error.message}`
      }, 500);
    }
  }

  /**
   * 清理测试数据
   */
  static async cleanupTestData(c: Context<{ Bindings: Env }>) {
    try {
      const dataPool = new DataPoolService(c.env);
      await dataPool.cleanupTestData();

      return c.json({
        success: true,
        message: '测试数据清理完成',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      return c.json({
        success: false,
        error: `清理测试数据失败: ${error.message}`
      }, 500);
    }
  }

  /**
   * 获取性能指标
   */
  static async getPerformanceMetrics(c: Context<{ Bindings: Env }>) {
    try {
      const monitoring = new MonitoringService(c.env);
      const metrics = await monitoring.getPerformanceMetrics();

      return c.json({
        success: true,
        data: metrics
      });
    } catch (error) {
      return c.json({
        success: false,
        error: `获取性能指标失败: ${error.message}`
      }, 500);
    }
  }

  /**
   * 获取错误日志
   */
  static async getErrorLogs(c: Context<{ Bindings: Env }>) {
    try {
      const { limit = 50 } = c.req.query();
      const monitoring = new MonitoringService(c.env);
      const errors = await monitoring.getErrorLogs(parseInt(limit));

      return c.json({
        success: true,
        data: errors
      });
    } catch (error) {
      return c.json({
        success: false,
        error: `获取错误日志失败: ${error.message}`
      }, 500);
    }
  }

  /**
   * 获取系统指标
   */
  static async getSystemMetrics(c: Context<{ Bindings: Env }>) {
    try {
      const monitoring = new MonitoringService(c.env);
      const metrics = await monitoring.getSystemMetrics();

      return c.json({
        success: true,
        data: metrics
      });
    } catch (error) {
      return c.json({
        success: false,
        error: `获取系统指标失败: ${error.message}`
      }, 500);
    }
  }
}

/**
 * 获取测试配置
 */
async function getTestConfig(env: Env): Promise<TestConfig> {
  try {
    const configStr = await env.TEST_CONFIG.get('current_config');
    if (configStr) {
      return JSON.parse(configStr);
    }
  } catch (error) {
    console.error('获取测试配置失败:', error);
  }

  // 返回默认配置
  return {
    enabled: false,
    intensity: {
      questionnaire: parseInt(env.DEFAULT_TEST_INTENSITY) || 10,
      story: 5,
      registration: 2
    },
    contentMix: {
      normal: 0.8,
      sensitive: 0.15,
      violation: 0.05
    },
    schedule: {
      activeHours: ['09:00-12:00', '14:00-18:00', '19:00-22:00'],
      timezone: 'Asia/Shanghai',
      weekendEnabled: true,
      intervalMinutes: 5
    },
    targets: {
      apiBase: env.TARGET_API_BASE,
      maxConcurrent: parseInt(env.MAX_CONCURRENT_REQUESTS) || 50,
      timeout: 30000
    }
  };
}

/**
 * 验证配置
 */
function validateConfig(config: any): boolean {
  try {
    // 基本结构检查
    if (!config || typeof config !== 'object') return false;
    
    // 检查必需字段
    if (typeof config.enabled !== 'boolean') return false;
    
    if (!config.intensity || 
        typeof config.intensity.questionnaire !== 'number' ||
        typeof config.intensity.story !== 'number' ||
        typeof config.intensity.registration !== 'number') {
      return false;
    }
    
    if (!config.contentMix ||
        typeof config.contentMix.normal !== 'number' ||
        typeof config.contentMix.sensitive !== 'number' ||
        typeof config.contentMix.violation !== 'number') {
      return false;
    }
    
    // 检查内容比例总和是否为1
    const total = config.contentMix.normal + config.contentMix.sensitive + config.contentMix.violation;
    if (Math.abs(total - 1) > 0.01) return false;
    
    if (!config.targets ||
        typeof config.targets.apiBase !== 'string' ||
        typeof config.targets.maxConcurrent !== 'number' ||
        typeof config.targets.timeout !== 'number') {
      return false;
    }
    
    return true;
  } catch (error) {
    return false;
  }
}
