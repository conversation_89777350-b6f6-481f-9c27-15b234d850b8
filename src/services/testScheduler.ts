/**
 * 测试调度器
 * 负责管理和执行各种测试任务
 */

import { Env } from '../index';
import { TestConfig, TaskResult, TestContentCombination } from '../types';
import { QuestionnaireGenerator } from '../generators/questionnaireGenerator';
import { StoryGenerator } from '../generators/storyGenerator';
import { UserGenerator } from '../generators/userGenerator';
import { MonitoringService } from './monitoringService';
import { DataPoolService } from './dataPoolService';

export class TestScheduler {
  private env: Env;
  private monitoring: MonitoringService;
  private dataPool: DataPoolService;

  constructor(env: Env) {
    this.env = env;
    this.monitoring = new MonitoringService(env);
    this.dataPool = new DataPoolService(env);
  }

  /**
   * 执行定时任务
   */
  async executeScheduledTasks(): Promise<void> {
    try {
      // 获取测试配置
      const config = await this.getTestConfig();
      
      if (!config.enabled) {
        console.log('测试已禁用，跳过执行');
        return;
      }

      // 检查是否在活跃时间段内
      if (!this.isActiveTime(config)) {
        console.log('当前不在活跃时间段，跳过执行');
        return;
      }

      // 检查是否到了发布时间
      if (!this.shouldExecuteNow(config)) {
        console.log('未到发布时间，跳过执行');
        return;
      }

      // 计算当前分钟的任务强度
      const intensity = this.calculateCurrentIntensity(config);
      
      console.log('执行测试任务:', {
        questionnaire: intensity.questionnaire,
        story: intensity.story,
        registration: intensity.registration
      });

      // 并发执行各类任务
      const tasks = await Promise.allSettled([
        this.executeQuestionnaireTests(intensity.questionnaire, config),
        this.executeStoryTests(intensity.story, config),
        this.executeRegistrationTests(intensity.registration, config)
      ]);

      // 收集执行结果
      await this.collectTaskResults(tasks);

      // 更新监控指标
      await this.updateMetrics(intensity, tasks);

    } catch (error) {
      console.error('执行定时任务失败:', error);
      await this.monitoring.logError('scheduler_error', error.message);
    }
  }

  /**
   * 执行问卷测试
   */
  private async executeQuestionnaireTests(count: number, config: TestConfig): Promise<TaskResult[]> {
    if (count === 0) return [];

    const results: TaskResult[] = [];

    for (let i = 0; i < count; i++) {
      try {
        // 从数据池获取测试组合
        const combination = await this.dataPool.getTestCombination('questionnaire');
        
        if (!combination) {
          console.warn('无法获取问卷测试组合，跳过');
          continue;
        }

        const result = await this.executeQuestionnaireTask(combination, config);
        results.push(result);

        // 任务间延迟，模拟真实用户行为
        if (i < count - 1) {
          await this.delay(Math.random() * 5000 + 2000); // 2-7秒随机延迟
        }
      } catch (error) {
        results.push({
          taskType: 'questionnaire',
          success: false,
          responseTime: 0,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }

    return results;
  }

  /**
   * 执行单个问卷任务
   */
  private async executeQuestionnaireTask(combination: TestContentCombination, config: TestConfig): Promise<TaskResult> {
    const startTime = Date.now();
    
    try {
      // 生成完整的问卷数据
      const generator = new QuestionnaireGenerator(this.env, config);
      const questionnaireData = await generator.generateFromCombination(combination);
      
      // 提交问卷
      const response = await this.submitQuestionnaire(questionnaireData, config);
      
      const responseTime = Date.now() - startTime;
      
      return {
        taskType: 'questionnaire',
        success: response.ok,
        responseTime,
        statusCode: response.status,
        timestamp: new Date().toISOString(),
        metadata: {
          userId: combination.user.uuid,
          contentType: combination.voice?.category || 'normal',
          expectedResult: combination.voice?.expectedResult || 'approve'
        }
      };
    } catch (error) {
      return {
        taskType: 'questionnaire',
        success: false,
        responseTime: Date.now() - startTime,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 执行故事测试
   */
  private async executeStoryTests(count: number, config: TestConfig): Promise<TaskResult[]> {
    if (count === 0) return [];

    const results: TaskResult[] = [];

    for (let i = 0; i < count; i++) {
      try {
        const combination = await this.dataPool.getTestCombination('story');
        
        if (!combination) {
          console.warn('无法获取故事测试组合，跳过');
          continue;
        }

        const result = await this.executeStoryTask(combination, config);
        results.push(result);

        // 任务间延迟
        if (i < count - 1) {
          await this.delay(Math.random() * 3000 + 1000); // 1-4秒随机延迟
        }
      } catch (error) {
        results.push({
          taskType: 'story',
          success: false,
          responseTime: 0,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }

    return results;
  }

  /**
   * 执行单个故事任务
   */
  private async executeStoryTask(combination: TestContentCombination, config: TestConfig): Promise<TaskResult> {
    const startTime = Date.now();
    
    try {
      const generator = new StoryGenerator(this.env, config);
      const storyData = await generator.generateFromCombination(combination);
      
      const response = await this.submitStory(storyData, config);
      
      return {
        taskType: 'story',
        success: response.ok,
        responseTime: Date.now() - startTime,
        statusCode: response.status,
        timestamp: new Date().toISOString(),
        metadata: {
          userId: combination.user.uuid,
          contentType: combination.story?.category || 'normal',
          expectedResult: combination.story?.expectedResult || 'approve'
        }
      };
    } catch (error) {
      return {
        taskType: 'story',
        success: false,
        responseTime: Date.now() - startTime,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 执行用户注册测试
   */
  private async executeRegistrationTests(count: number, config: TestConfig): Promise<TaskResult[]> {
    if (count === 0) return [];

    const results: TaskResult[] = [];

    for (let i = 0; i < count; i++) {
      try {
        const combination = await this.dataPool.getTestCombination('registration');
        
        if (!combination) {
          console.warn('无法获取注册测试组合，跳过');
          continue;
        }

        const result = await this.executeRegistrationTask(combination, config);
        results.push(result);

        // 任务间延迟
        if (i < count - 1) {
          await this.delay(Math.random() * 4000 + 2000); // 2-6秒随机延迟
        }
      } catch (error) {
        results.push({
          taskType: 'registration',
          success: false,
          responseTime: 0,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }

    return results;
  }

  /**
   * 执行单个注册任务
   */
  private async executeRegistrationTask(combination: TestContentCombination, config: TestConfig): Promise<TaskResult> {
    const startTime = Date.now();
    
    try {
      const generator = new UserGenerator(this.env, config);
      const userData = await generator.generateFromCombination(combination);
      
      const response = await this.submitRegistration(userData, config);
      
      return {
        taskType: 'registration',
        success: response.ok,
        responseTime: Date.now() - startTime,
        statusCode: response.status,
        timestamp: new Date().toISOString(),
        metadata: {
          userId: combination.user.uuid,
          userType: 'semi_anonymous'
        }
      };
    } catch (error) {
      return {
        taskType: 'registration',
        success: false,
        responseTime: Date.now() - startTime,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 提交问卷数据
   */
  private async submitQuestionnaire(data: any, config: TestConfig): Promise<Response> {
    const url = `${config.targets.apiBase}/api/questionnaire/submit`;
    
    return fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TestRobot/1.0',
        'X-Test-Source': 'automated-test-robot',
        'X-Test-Session': data._testMeta?.testSessionId || 'unknown',
        'X-Test-Robot-Id': data._testMeta?.robotId || 'test-robot-1'
      },
      body: JSON.stringify(data),
      signal: AbortSignal.timeout(config.targets.timeout)
    });
  }

  /**
   * 提交故事数据
   */
  private async submitStory(data: any, config: TestConfig): Promise<Response> {
    const url = `${config.targets.apiBase}/api/story/submit`;
    
    return fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TestRobot/1.0',
        'X-Test-Source': 'automated-test-robot',
        'X-Test-Session': data._testMeta?.testSessionId || 'unknown',
        'X-Test-Robot-Id': data._testMeta?.robotId || 'test-robot-1'
      },
      body: JSON.stringify(data),
      signal: AbortSignal.timeout(config.targets.timeout)
    });
  }

  /**
   * 提交注册数据
   */
  private async submitRegistration(data: any, config: TestConfig): Promise<Response> {
    const url = `${config.targets.apiBase}/api/auth/register`;
    
    return fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TestRobot/1.0',
        'X-Test-Source': 'automated-test-robot',
        'X-Test-Session': data._testMeta?.testSessionId || 'unknown',
        'X-Test-Robot-Id': data._testMeta?.robotId || 'test-robot-1'
      },
      body: JSON.stringify(data),
      signal: AbortSignal.timeout(config.targets.timeout)
    });
  }

  /**
   * 获取测试配置
   */
  private async getTestConfig(): Promise<TestConfig> {
    try {
      const configStr = await this.env.TEST_CONFIG.get('current_config');
      if (configStr) {
        return JSON.parse(configStr);
      }
    } catch (error) {
      console.error('获取测试配置失败:', error);
    }

    // 返回默认配置
    return this.getDefaultConfig();
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): TestConfig {
    return {
      enabled: false,
      intensity: {
        questionnaire: parseInt(this.env.DEFAULT_TEST_INTENSITY) || 10,
        story: 5,
        registration: 2
      },
      contentMix: {
        normal: 0.8,
        sensitive: 0.15,
        violation: 0.05
      },
      schedule: {
        activeHours: ['09:00-12:00', '14:00-18:00', '19:00-22:00'],
        timezone: 'Asia/Shanghai',
        weekendEnabled: true,
        intervalMinutes: 5 // 默认5分钟间隔
      },
      targets: {
        apiBase: this.env.TARGET_API_BASE,
        maxConcurrent: parseInt(this.env.MAX_CONCURRENT_REQUESTS) || 50,
        timeout: 30000
      }
    };
  }

  /**
   * 检查是否在活跃时间段
   */
  private isActiveTime(config: TestConfig): boolean {
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentTime = currentHour * 60 + currentMinute;

    // 检查是否是周末
    if (!config.schedule.weekendEnabled) {
      const dayOfWeek = now.getDay();
      if (dayOfWeek === 0 || dayOfWeek === 6) {
        return false;
      }
    }

    // 检查是否在活跃时间段内
    for (const timeRange of config.schedule.activeHours) {
      const [start, end] = timeRange.split('-');
      const [startHour, startMinute] = start.split(':').map(Number);
      const [endHour, endMinute] = end.split(':').map(Number);
      
      const startTime = startHour * 60 + startMinute;
      const endTime = endHour * 60 + endMinute;
      
      if (currentTime >= startTime && currentTime <= endTime) {
        return true;
      }
    }

    return false;
  }

  /**
   * 检查是否应该现在执行
   */
  private shouldExecuteNow(config: TestConfig): boolean {
    const now = new Date();
    const currentMinute = now.getMinutes();
    
    // 根据间隔时间判断是否应该执行
    return currentMinute % config.schedule.intervalMinutes === 0;
  }

  /**
   * 计算当前强度
   */
  private calculateCurrentIntensity(config: TestConfig): TestConfig['intensity'] {
    const now = new Date();
    const hour = now.getHours();
    
    let multiplier = 1;
    
    // 高峰时段增加强度
    if ((hour >= 9 && hour <= 11) || (hour >= 14 && hour <= 16) || (hour >= 19 && hour <= 21)) {
      multiplier = 1.5;
    }
    
    // 深夜降低强度
    if (hour >= 23 || hour <= 6) {
      multiplier = 0.3;
    }

    return {
      questionnaire: Math.round(config.intensity.questionnaire * multiplier),
      story: Math.round(config.intensity.story * multiplier),
      registration: Math.round(config.intensity.registration * multiplier)
    };
  }

  /**
   * 收集任务结果
   */
  private async collectTaskResults(tasks: PromiseSettledResult<TaskResult[]>[]): Promise<void> {
    const allResults: TaskResult[] = [];
    
    for (const task of tasks) {
      if (task.status === 'fulfilled') {
        allResults.push(...task.value);
      }
    }

    // 存储结果到KV
    const timestamp = new Date().toISOString();
    const key = `results_${timestamp.split('T')[0]}_${Date.now()}`;
    
    await this.env.TEST_RESULTS.put(key, JSON.stringify({
      timestamp,
      results: allResults,
      summary: this.summarizeResults(allResults)
    }), {
      expirationTtl: 7 * 24 * 60 * 60 // 7天过期
    });
  }

  /**
   * 汇总结果
   */
  private summarizeResults(results: TaskResult[]): any {
    const summary = {
      total: results.length,
      success: 0,
      failure: 0,
      avgResponseTime: 0,
      byType: {} as any
    };

    let totalResponseTime = 0;

    for (const result of results) {
      if (result.success) {
        summary.success++;
      } else {
        summary.failure++;
      }

      totalResponseTime += result.responseTime;

      if (!summary.byType[result.taskType]) {
        summary.byType[result.taskType] = { total: 0, success: 0, failure: 0 };
      }
      
      summary.byType[result.taskType].total++;
      if (result.success) {
        summary.byType[result.taskType].success++;
      } else {
        summary.byType[result.taskType].failure++;
      }
    }

    summary.avgResponseTime = results.length > 0 ? totalResponseTime / results.length : 0;

    return summary;
  }

  /**
   * 更新监控指标
   */
  private async updateMetrics(intensity: TestConfig['intensity'], tasks: PromiseSettledResult<TaskResult[]>[]): Promise<void> {
    const metrics = {
      timestamp: new Date().toISOString(),
      planned: intensity,
      executed: {
        questionnaire: 0,
        story: 0,
        registration: 0
      },
      performance: {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        avgResponseTime: 0
      }
    };

    // 计算实际执行数量和性能指标
    let totalResponseTime = 0;
    let totalRequests = 0;

    for (const task of tasks) {
      if (task.status === 'fulfilled') {
        for (const result of task.value) {
          totalRequests++;
          totalResponseTime += result.responseTime;
          
          if (result.success) {
            metrics.performance.successfulRequests++;
          } else {
            metrics.performance.failedRequests++;
          }

          metrics.executed[result.taskType as keyof typeof metrics.executed]++;
        }
      }
    }

    metrics.performance.totalRequests = totalRequests;
    metrics.performance.avgResponseTime = totalRequests > 0 ? totalResponseTime / totalRequests : 0;

    // 存储指标
    const key = `metrics_${Date.now()}`;
    await this.env.TEST_METRICS.put(key, JSON.stringify(metrics), {
      expirationTtl: 30 * 24 * 60 * 60 // 30天过期
    });
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
