/**
 * 数据池服务
 * 管理测试用户、问卷心声、故事等数据池
 */

import { Env } from '../index';
import { 
  UserPool, 
  QuestionnaireVoicePool, 
  StoryPool, 
  TestContentCombination,
  TestDataIdentifier,
  ContentTemplate,
  VariablePool
} from '../types';

export class DataPoolService {
  private env: Env;

  constructor(env: Env) {
    this.env = env;
  }

  /**
   * 初始化所有数据池
   */
  async initializeDataPools(): Promise<void> {
    console.log('开始初始化数据池...');
    
    await Promise.all([
      this.initializeUserPool(),
      this.initializeVoicePool(),
      this.initializeStoryPool(),
      this.initializeTemplates(),
      this.initializeVariables()
    ]);
    
    console.log('数据池初始化完成');
  }

  /**
   * 初始化用户池 - 100个A+B半匿名UUID
   */
  async initializeUserPool(): Promise<void> {
    const users: UserPool[] = [];
    
    const grades = ['大一', '大二', '大三', '大四', '研一', '研二', '研三'];
    const majors = [
      '计算机科学与技术', '软件工程', '网络工程', '信息安全', '数据科学与大数据技术',
      '人工智能', '物联网工程', '电子信息工程', '通信工程', '自动化',
      '机械工程', '电气工程', '土木工程', '化学工程', '材料科学与工程',
      '经济学', '金融学', '会计学', '市场营销', '国际贸易',
      '法学', '汉语言文学', '英语', '新闻学', '广告学'
    ];
    const schools = [
      '清华大学', '北京大学', '复旦大学', '上海交通大学', '浙江大学',
      '中国科学技术大学', '南京大学', '华中科技大学', '中山大学', '西安交通大学',
      '北京理工大学', '天津大学', '东南大学', '华南理工大学', '大连理工大学'
    ];
    const locations = ['北京', '上海', '深圳', '杭州', '广州', '成都', '武汉', '西安', '南京', '苏州'];

    for (let i = 0; i < 100; i++) {
      const user: UserPool = {
        id: `user_${i + 1}`,
        uuid: this.generateUUID(),
        profile: {
          grade: this.randomChoice(grades),
          major: this.randomChoice(majors),
          gender: this.randomChoice(['男', '女', '其他']),
          age: this.randomInt(18, 28),
          school: this.randomChoice(schools),
          location: this.randomChoice(locations)
        },
        createdAt: new Date().toISOString(),
        usageCount: 0
      };
      
      users.push(user);
    }

    await this.env.TEST_DATA_POOL.put('user_pool', JSON.stringify(users));
    console.log(`用户池初始化完成: ${users.length} 个用户`);
  }

  /**
   * 初始化问卷心声池 - 100条正常 + 50条敏感
   */
  async initializeVoicePool(): Promise<void> {
    const voices: QuestionnaireVoicePool[] = [];
    
    // 100条正常内容
    const normalTemplates = [
      '作为{grade}的{major}学生，我觉得{skill}技能对找工作很重要。希望学校能提供更多{resource}方面的支持。',
      '通过大学的学习，我发现了自己对{field}领域的兴趣，也明确了未来的发展方向。',
      '我认为持续学习和实践是成功的关键，会努力提升自己在{area}方面的能力。',
      '希望能找到一份既有挑战性又能发挥自己{strength}特长的工作。',
      '我相信通过努力和坚持，一定能在{industry}行业实现自己的职业目标。'
    ];

    for (let i = 0; i < 100; i++) {
      const voice: QuestionnaireVoicePool = {
        id: `voice_normal_${i + 1}`,
        content: '', // 将在使用时生成
        category: 'normal',
        template: this.randomChoice(normalTemplates),
        variables: {},
        expectedResult: 'approve',
        tags: ['正常', '积极', '建设性'],
        createdAt: new Date().toISOString(),
        usageCount: 0
      };
      
      voices.push(voice);
    }

    // 50条敏感内容
    const sensitiveTemplates = [
      '现在就业压力太大了，{emotion}。家里经济条件{condition}，担心{worry}。',
      '看到同学们都比我优秀，我很{feeling}，对未来感到{concern}。',
      '感觉自己能力不足，很{emotion}，不知道毕业后能不能找到工作。',
      '现在的教育制度让人{feeling}，学的东西和实际工作差距太大。',
      '家庭压力很大，父母期望很高，但我觉得自己{emotion}。'
    ];

    for (let i = 0; i < 50; i++) {
      const voice: QuestionnaireVoicePool = {
        id: `voice_sensitive_${i + 1}`,
        content: '',
        category: 'sensitive',
        template: this.randomChoice(sensitiveTemplates),
        variables: {},
        expectedResult: 'approve', // 敏感但不违规
        tags: ['敏感', '情绪化', '压力'],
        createdAt: new Date().toISOString(),
        usageCount: 0
      };
      
      voices.push(voice);
    }

    await this.env.TEST_DATA_POOL.put('voice_pool', JSON.stringify(voices));
    console.log(`问卷心声池初始化完成: ${voices.length} 条内容`);
  }

  /**
   * 初始化故事池 - 100条正常 + 50条违规
   */
  async initializeStoryPool(): Promise<void> {
    const stories: StoryPool[] = [];
    
    // 100条正常故事
    const normalStoryTemplates = [
      {
        title: '我的{field}学习之路',
        content: '作为一名{major}专业的学生，我在{field}领域的学习经历让我收获很多。从最初的{initial_state}到现在的{current_state}，这个过程充满了{experience}。我特别感谢{person}的帮助，让我在{skill}方面有了很大提升。未来我希望能在{goal}方向继续发展。'
      },
      {
        title: '实习经历分享',
        content: '在{company}实习的{duration}里，我学到了很多{skill}相关的知识。虽然刚开始{challenge}，但通过{method}，我逐渐适应了工作节奏。这次实习让我明白了{insight}，也为我的职业规划提供了{guidance}。'
      }
    ];

    for (let i = 0; i < 100; i++) {
      const template = this.randomChoice(normalStoryTemplates);
      const story: StoryPool = {
        id: `story_normal_${i + 1}`,
        title: template.title,
        content: template.content,
        category: 'normal',
        template: template.content,
        variables: {},
        expectedResult: 'approve',
        tags: ['正常', '分享', '经验'],
        createdAt: new Date().toISOString(),
        usageCount: 0
      };
      
      stories.push(story);
    }

    // 50条违规故事
    const violationStoryTemplates = [
      {
        title: '对学校的不满',
        content: '这个学校就是{negative_word}，老师都是{insult}，什么都不会教。学费这么贵，教学质量{complaint}。我要{threat}这种不合理的制度。'
      },
      {
        title: '就业市场吐槽',
        content: '现在的企业都是{negative_word}，只会{exploitation}员工。HR都是{insult}，面试官{complaint}。这个社会就是{negative_view}，年轻人没有出路。'
      }
    ];

    for (let i = 0; i < 50; i++) {
      const template = this.randomChoice(violationStoryTemplates);
      const story: StoryPool = {
        id: `story_violation_${i + 1}`,
        title: template.title,
        content: template.content,
        category: 'violation',
        template: template.content,
        variables: {},
        expectedResult: 'reject',
        tags: ['违规', '负面', '攻击性'],
        createdAt: new Date().toISOString(),
        usageCount: 0
      };
      
      stories.push(story);
    }

    await this.env.TEST_DATA_POOL.put('story_pool', JSON.stringify(stories));
    console.log(`故事池初始化完成: ${stories.length} 条故事`);
  }

  /**
   * 初始化内容模板
   */
  async initializeTemplates(): Promise<void> {
    const templates: ContentTemplate[] = [
      // 问卷心声模板
      {
        id: 'voice_normal_1',
        type: 'questionnaire_voice',
        category: 'normal',
        template: '作为{grade}的{major}学生，我觉得{skill}技能对找工作很重要。',
        variables: ['grade', 'major', 'skill'],
        tags: ['技能', '就业'],
        expectedResult: 'approve',
        weight: 1.0
      },
      {
        id: 'voice_sensitive_1',
        type: 'questionnaire_voice',
        category: 'sensitive',
        template: '现在就业压力太大了，{emotion}。担心{worry}。',
        variables: ['emotion', 'worry'],
        tags: ['压力', '焦虑'],
        expectedResult: 'approve',
        weight: 1.0
      },
      // 故事模板
      {
        id: 'story_normal_1',
        type: 'story',
        category: 'normal',
        template: '我的{field}学习经历让我收获很多。从{initial}到{current}，这个过程{experience}。',
        variables: ['field', 'initial', 'current', 'experience'],
        tags: ['学习', '成长'],
        expectedResult: 'approve',
        weight: 1.0
      }
    ];

    await this.env.TEST_DATA_POOL.put('templates', JSON.stringify(templates));
    console.log(`模板初始化完成: ${templates.length} 个模板`);
  }

  /**
   * 初始化变量池
   */
  async initializeVariables(): Promise<void> {
    const variables: VariablePool = {
      grades: ['大一', '大二', '大三', '大四', '研一', '研二', '研三'],
      majors: ['计算机科学', '软件工程', '数据科学', '人工智能', '电子工程'],
      schools: ['清华大学', '北京大学', '复旦大学', '上海交通大学'],
      locations: ['北京', '上海', '深圳', '杭州', '广州'],
      skills: ['编程', '数据分析', '项目管理', '沟通协调', '创新思维'],
      emotions: ['很焦虑', '有点担心', '压力很大', '感到迷茫'],
      concerns: ['找不到工作', '能力不足', '竞争激烈', '经济压力'],
      suggestions: ['增加实践机会', '提供就业指导', '改善教学质量'],
      // 敏感内容变量
      sensitiveEmotions: ['很沮丧', '非常焦虑', '极度担心', '深感绝望'],
      sensitiveTopics: ['家庭经济困难', '就业歧视', '学业压力'],
      // 违规内容变量
      violationWords: ['垃圾', '废物', '骗子', '黑心'],
      violationPhrases: ['完全没用', '纯属扯淡', '就是骗钱', '应该关闭']
    };

    await this.env.TEST_DATA_POOL.put('variables', JSON.stringify(variables));
    console.log('变量池初始化完成');
  }

  /**
   * 获取测试组合
   */
  async getTestCombination(type: 'questionnaire' | 'story' | 'registration'): Promise<TestContentCombination | null> {
    try {
      // 获取用户池
      const userPoolStr = await this.env.TEST_DATA_POOL.get('user_pool');
      if (!userPoolStr) {
        console.error('用户池未初始化');
        return null;
      }
      
      const userPool: UserPool[] = JSON.parse(userPoolStr);
      const user = this.randomChoice(userPool);

      // 生成测试标识
      const testMeta: TestDataIdentifier = {
        source: 'test-robot',
        testSessionId: this.generateSessionId(),
        robotId: 'test-robot-1',
        timestamp: new Date().toISOString(),
        category: 'synthetic',
        autoCleanup: true,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7天后过期
      };

      const combination: TestContentCombination = {
        user,
        testMeta
      };

      // 根据类型添加相应内容
      if (type === 'questionnaire') {
        const voicePoolStr = await this.env.TEST_DATA_POOL.get('voice_pool');
        if (voicePoolStr) {
          const voicePool: QuestionnaireVoicePool[] = JSON.parse(voicePoolStr);
          combination.voice = this.randomChoice(voicePool);
        }
      } else if (type === 'story') {
        const storyPoolStr = await this.env.TEST_DATA_POOL.get('story_pool');
        if (storyPoolStr) {
          const storyPool: StoryPool[] = JSON.parse(storyPoolStr);
          combination.story = this.randomChoice(storyPool);
        }
      }

      return combination;
    } catch (error) {
      console.error('获取测试组合失败:', error);
      return null;
    }
  }

  /**
   * 获取数据池统计
   */
  async getDataPoolStats(): Promise<any> {
    try {
      const [userPoolStr, voicePoolStr, storyPoolStr] = await Promise.all([
        this.env.TEST_DATA_POOL.get('user_pool'),
        this.env.TEST_DATA_POOL.get('voice_pool'),
        this.env.TEST_DATA_POOL.get('story_pool')
      ]);

      const userPool = userPoolStr ? JSON.parse(userPoolStr) : [];
      const voicePool = voicePoolStr ? JSON.parse(voicePoolStr) : [];
      const storyPool = storyPoolStr ? JSON.parse(storyPoolStr) : [];

      return {
        users: {
          total: userPool.length,
          byGrade: this.groupBy(userPool, 'profile.grade'),
          byMajor: this.groupBy(userPool, 'profile.major')
        },
        voices: {
          total: voicePool.length,
          byCategory: this.groupBy(voicePool, 'category'),
          normal: voicePool.filter(v => v.category === 'normal').length,
          sensitive: voicePool.filter(v => v.category === 'sensitive').length
        },
        stories: {
          total: storyPool.length,
          byCategory: this.groupBy(storyPool, 'category'),
          normal: storyPool.filter(s => s.category === 'normal').length,
          violation: storyPool.filter(s => s.category === 'violation').length
        },
        combinations: {
          questionnaire: userPool.length * voicePool.length,
          story: userPool.length * storyPool.length,
          total: userPool.length * (voicePool.length + storyPool.length)
        }
      };
    } catch (error) {
      console.error('获取数据池统计失败:', error);
      return null;
    }
  }

  /**
   * 清理测试数据
   */
  async cleanupTestData(): Promise<void> {
    console.log('开始清理测试数据...');
    
    // 清理过期的测试结果
    // 这里可以实现更复杂的清理逻辑
    
    console.log('测试数据清理完成');
  }

  // 辅助方法

  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private randomChoice<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }

  private randomInt(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  private groupBy(array: any[], key: string): Record<string, number> {
    const result: Record<string, number> = {};
    
    for (const item of array) {
      const value = this.getNestedValue(item, key);
      result[value] = (result[value] || 0) + 1;
    }
    
    return result;
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }
}
