/**
 * 问卷测试机器人 - 主入口
 * 用于大学生问卷系统的长周期性能和功能测试
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { TestScheduler } from './services/testScheduler';
import { TestController } from './controllers/testController';
import { MonitoringService } from './services/monitoringService';

export interface Env {
  // KV存储
  TEST_CONFIG: KVNamespace;
  TEST_RESULTS: KVNamespace;
  TEST_METRICS: KVNamespace;
  TEST_DATA_POOL: KVNamespace;
  
  // D1数据库
  TEST_DB: D1Database;
  
  // 环境变量
  TARGET_API_BASE: string;
  TEST_MODE: string;
  MAX_CONCURRENT_REQUESTS: string;
  DEFAULT_TEST_INTENSITY: string;
}

const app = new Hono<{ Bindings: Env }>();

// CORS配置
app.use('*', cors({
  origin: ['http://localhost:3000', 'https://*.pages.dev'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}));

// 健康检查
app.get('/', (c) => {
  return c.json({
    service: '问卷测试机器人',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    environment: c.env.TEST_MODE,
    target: c.env.TARGET_API_BASE
  });
});

// 测试控制API
app.get('/api/status', TestController.getStatus);
app.get('/api/config', TestController.getConfig);
app.post('/api/config', TestController.updateConfig);
app.post('/api/start', TestController.startTest);
app.post('/api/stop', TestController.stopTest);
app.get('/api/metrics', TestController.getMetrics);
app.get('/api/reports', TestController.getReports);

// 手动触发测试
app.post('/api/trigger/questionnaire', TestController.triggerQuestionnaire);
app.post('/api/trigger/story', TestController.triggerStory);
app.post('/api/trigger/registration', TestController.triggerRegistration);

// 数据管理API
app.post('/api/data/init-pools', TestController.initDataPools);
app.get('/api/data/pools', TestController.getDataPools);
app.post('/api/data/cleanup', TestController.cleanupTestData);

// 监控API
app.get('/api/monitor/performance', TestController.getPerformanceMetrics);
app.get('/api/monitor/errors', TestController.getErrorLogs);
app.get('/api/monitor/system', TestController.getSystemMetrics);

// 定时任务处理器
export default {
  fetch: app.fetch,
  
  // Cron触发器 - 每分钟执行
  async scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
    console.log('定时任务触发:', new Date().toISOString());
    
    try {
      const scheduler = new TestScheduler(env);
      await scheduler.executeScheduledTasks();
    } catch (error) {
      console.error('定时任务执行失败:', error);
      
      // 记录错误到监控系统
      const monitoring = new MonitoringService(env);
      await monitoring.logError('scheduled_task_error', error.message, {
        timestamp: new Date().toISOString(),
        event: event.cron
      });
    }
  }
};
