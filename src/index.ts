/**
 * 问卷测试机器人 - 主入口
 * 用于大学生问卷系统的长周期性能和功能测试
 */

import { Hono } from 'hono';

export interface Env {
  // KV存储
  TEST_CONFIG: KVNamespace;
  TEST_RESULTS: KVNamespace;
  TEST_METRICS: KVNamespace;
  TEST_DATA_POOL: KVNamespace;
  
  // D1数据库
  TEST_DB: D1Database;
  
  // 环境变量
  TARGET_API_BASE: string;
  TEST_MODE: string;
  MAX_CONCURRENT_REQUESTS: string;
  DEFAULT_TEST_INTENSITY: string;
}

const app = new Hono<{ Bindings: Env }>();

// 健康检查
app.get('/', (c) => {
  return c.json({
    service: '问卷测试机器人',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    environment: c.env.TEST_MODE || 'development',
    target: c.env.TARGET_API_BASE || 'not-configured'
  });
});

// 系统状态
app.get('/api/status', (c) => {
  return c.json({
    success: true,
    data: {
      status: 'stopped',
      message: '测试机器人已就绪，等待配置和启动',
      timestamp: new Date().toISOString()
    }
  });
});

// 启动测试
app.post('/api/start', async (c) => {
  return c.json({
    success: true,
    message: '测试启动功能开发中',
    timestamp: new Date().toISOString()
  });
});

// 停止测试
app.post('/api/stop', async (c) => {
  return c.json({
    success: true,
    message: '测试停止功能开发中',
    timestamp: new Date().toISOString()
  });
});

// 绑定诊断端点
app.get('/api/debug/bindings', (c) => {
  const bindings = {
    kv: {
      TEST_CONFIG: !!c.env.TEST_CONFIG,
      TEST_RESULTS: !!c.env.TEST_RESULTS,
      TEST_METRICS: !!c.env.TEST_METRICS,
      TEST_DATA_POOL: !!c.env.TEST_DATA_POOL
    },
    d1: {
      TEST_DB: !!c.env.TEST_DB
    },
    env: {
      TARGET_API_BASE: !!c.env.TARGET_API_BASE,
      TEST_MODE: c.env.TEST_MODE,
      ENVIRONMENT: c.env.ENVIRONMENT
    }
  };

  return c.json({
    success: true,
    bindings,
    timestamp: new Date().toISOString()
  });
});

// 初始化数据池
app.post('/api/data/init-pools', async (c) => {
  try {
    console.log('开始初始化数据池...');

    // 初始化用户池 - 100个A+B半匿名UUID
    const userPool = await initializeUserPool();
    await c.env.TEST_DATA_POOL.put('user_pool', JSON.stringify(userPool));

    // 初始化问卷心声池 - 100条正常 + 50条敏感
    const voicePool = await initializeVoicePool();
    await c.env.TEST_DATA_POOL.put('voice_pool', JSON.stringify(voicePool));

    // 初始化故事池 - 100条正常 + 50条违规
    const storyPool = await initializeStoryPool();
    await c.env.TEST_DATA_POOL.put('story_pool', JSON.stringify(storyPool));

    // 初始化变量池
    const variablePool = await initializeVariablePool();
    await c.env.TEST_DATA_POOL.put('variable_pool', JSON.stringify(variablePool));

    // 记录初始化时间
    await c.env.TEST_CONFIG.put('last_init_time', new Date().toISOString());

    console.log('数据池初始化完成');

    return c.json({
      success: true,
      message: '数据池初始化成功',
      data: {
        userPool: userPool.length,
        voicePool: voicePool.length,
        storyPool: storyPool.length,
        totalCombinations: userPool.length * (voicePool.length + storyPool.length)
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('数据池初始化失败:', error);
    return c.json({
      success: false,
      error: `数据池初始化失败: ${error.message}`,
      timestamp: new Date().toISOString()
    }, 500);
  }
});

// 获取数据池统计
app.get('/api/data/pools', async (c) => {
  try {
    const [userPoolStr, voicePoolStr, storyPoolStr] = await Promise.all([
      c.env.TEST_DATA_POOL.get('user_pool'),
      c.env.TEST_DATA_POOL.get('voice_pool'),
      c.env.TEST_DATA_POOL.get('story_pool')
    ]);

    const userPool = userPoolStr ? JSON.parse(userPoolStr) : [];
    const voicePool = voicePoolStr ? JSON.parse(voicePoolStr) : [];
    const storyPool = storyPoolStr ? JSON.parse(storyPoolStr) : [];

    return c.json({
      success: true,
      data: {
        users: {
          total: userPool.length,
          byGrade: groupBy(userPool, 'profile.grade'),
          byMajor: groupBy(userPool, 'profile.major')
        },
        voices: {
          total: voicePool.length,
          byCategory: groupBy(voicePool, 'category'),
          normal: voicePool.filter(v => v.category === 'normal').length,
          sensitive: voicePool.filter(v => v.category === 'sensitive').length
        },
        stories: {
          total: storyPool.length,
          byCategory: groupBy(storyPool, 'category'),
          normal: storyPool.filter(s => s.category === 'normal').length,
          violation: storyPool.filter(s => s.category === 'violation').length
        },
        combinations: {
          questionnaire: userPool.length * voicePool.length,
          story: userPool.length * storyPool.length,
          total: userPool.length * (voicePool.length + storyPool.length)
        }
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return c.json({
      success: false,
      error: `获取数据池统计失败: ${error.message}`,
      timestamp: new Date().toISOString()
    }, 500);
  }
});

// 数据池初始化函数
async function initializeUserPool() {
  const users = [];
  const grades = ['大一', '大二', '大三', '大四', '研一', '研二', '研三'];
  const majors = [
    '计算机科学与技术', '软件工程', '网络工程', '信息安全', '数据科学与大数据技术',
    '人工智能', '物联网工程', '电子信息工程', '通信工程', '自动化',
    '机械工程', '电气工程', '土木工程', '化学工程', '材料科学与工程',
    '经济学', '金融学', '会计学', '市场营销', '国际贸易',
    '法学', '汉语言文学', '英语', '新闻学', '广告学'
  ];
  const schools = [
    '清华大学', '北京大学', '复旦大学', '上海交通大学', '浙江大学',
    '中国科学技术大学', '南京大学', '华中科技大学', '中山大学', '西安交通大学',
    '北京理工大学', '天津大学', '东南大学', '华南理工大学', '大连理工大学'
  ];
  const locations = ['北京', '上海', '深圳', '杭州', '广州', '成都', '武汉', '西安', '南京', '苏州'];

  for (let i = 0; i < 100; i++) {
    const user = {
      id: `user_${i + 1}`,
      uuid: generateUUID(),
      profile: {
        grade: randomChoice(grades),
        major: randomChoice(majors),
        gender: randomChoice(['男', '女', '其他']),
        age: randomInt(18, 28),
        school: randomChoice(schools),
        location: randomChoice(locations)
      },
      createdAt: new Date().toISOString(),
      usageCount: 0
    };
    users.push(user);
  }

  return users;
}

async function initializeVoicePool() {
  const voices = [];

  // 100条正常内容
  const normalTemplates = [
    '作为{grade}的{major}学生，我觉得{skill}技能对找工作很重要。希望学校能提供更多{resource}方面的支持。',
    '通过大学的学习，我发现了自己对{field}领域的兴趣，也明确了未来的发展方向。',
    '我认为持续学习和实践是成功的关键，会努力提升自己在{area}方面的能力。',
    '希望能找到一份既有挑战性又能发挥自己{strength}特长的工作。',
    '我相信通过努力和坚持，一定能在{industry}行业实现自己的职业目标。'
  ];

  for (let i = 0; i < 100; i++) {
    const voice = {
      id: `voice_normal_${i + 1}`,
      content: randomChoice(normalTemplates),
      category: 'normal',
      template: randomChoice(normalTemplates),
      variables: {},
      expectedResult: 'approve',
      tags: ['正常', '积极', '建设性'],
      createdAt: new Date().toISOString(),
      usageCount: 0
    };
    voices.push(voice);
  }

  // 50条敏感内容
  const sensitiveTemplates = [
    '现在就业压力太大了，{emotion}。家里经济条件{condition}，担心{worry}。',
    '看到同学们都比我优秀，我很{feeling}，对未来感到{concern}。',
    '感觉自己能力不足，很{emotion}，不知道毕业后能不能找到工作。',
    '现在的教育制度让人{feeling}，学的东西和实际工作差距太大。',
    '家庭压力很大，父母期望很高，但我觉得自己{emotion}。'
  ];

  for (let i = 0; i < 50; i++) {
    const voice = {
      id: `voice_sensitive_${i + 1}`,
      content: randomChoice(sensitiveTemplates),
      category: 'sensitive',
      template: randomChoice(sensitiveTemplates),
      variables: {},
      expectedResult: 'approve', // 敏感但不违规
      tags: ['敏感', '情绪化', '压力'],
      createdAt: new Date().toISOString(),
      usageCount: 0
    };
    voices.push(voice);
  }

  return voices;
}

async function initializeStoryPool() {
  const stories = [];

  // 100条正常故事
  const normalStoryTemplates = [
    {
      title: '我的{field}学习之路',
      content: '作为一名{major}专业的学生，我在{field}领域的学习经历让我收获很多。从最初的{initial_state}到现在的{current_state}，这个过程充满了{experience}。我特别感谢{person}的帮助，让我在{skill}方面有了很大提升。未来我希望能在{goal}方向继续发展。'
    },
    {
      title: '实习经历分享',
      content: '在{company}实习的{duration}里，我学到了很多{skill}相关的知识。虽然刚开始{challenge}，但通过{method}，我逐渐适应了工作节奏。这次实习让我明白了{insight}，也为我的职业规划提供了{guidance}。'
    }
  ];

  for (let i = 0; i < 100; i++) {
    const template = randomChoice(normalStoryTemplates);
    const story = {
      id: `story_normal_${i + 1}`,
      title: template.title,
      content: template.content,
      category: 'normal',
      template: template.content,
      variables: {},
      expectedResult: 'approve',
      tags: ['正常', '分享', '经验'],
      createdAt: new Date().toISOString(),
      usageCount: 0
    };
    stories.push(story);
  }

  // 50条违规故事
  const violationStoryTemplates = [
    {
      title: '对学校的不满',
      content: '这个学校就是{negative_word}，老师都是{insult}，什么都不会教。学费这么贵，教学质量{complaint}。我要{threat}这种不合理的制度。'
    },
    {
      title: '就业市场吐槽',
      content: '现在的企业都是{negative_word}，只会{exploitation}员工。HR都是{insult}，面试官{complaint}。这个社会就是{negative_view}，年轻人没有出路。'
    }
  ];

  for (let i = 0; i < 50; i++) {
    const template = randomChoice(violationStoryTemplates);
    const story = {
      id: `story_violation_${i + 1}`,
      title: template.title,
      content: template.content,
      category: 'violation',
      template: template.content,
      variables: {},
      expectedResult: 'reject',
      tags: ['违规', '负面', '攻击性'],
      createdAt: new Date().toISOString(),
      usageCount: 0
    };
    stories.push(story);
  }

  return stories;
}

async function initializeVariablePool() {
  return {
    grades: ['大一', '大二', '大三', '大四', '研一', '研二', '研三'],
    majors: ['计算机科学', '软件工程', '数据科学', '人工智能', '电子工程'],
    schools: ['清华大学', '北京大学', '复旦大学', '上海交通大学'],
    locations: ['北京', '上海', '深圳', '杭州', '广州'],
    skills: ['编程', '数据分析', '项目管理', '沟通协调', '创新思维'],
    emotions: ['很焦虑', '有点担心', '压力很大', '感到迷茫'],
    concerns: ['找不到工作', '能力不足', '竞争激烈', '经济压力'],
    suggestions: ['增加实践机会', '提供就业指导', '改善教学质量'],
    sensitiveEmotions: ['很沮丧', '非常焦虑', '极度担心', '深感绝望'],
    sensitiveTopics: ['家庭经济困难', '就业歧视', '学业压力'],
    violationWords: ['垃圾', '废物', '骗子', '黑心'],
    violationPhrases: ['完全没用', '纯属扯淡', '就是骗钱', '应该关闭']
  };
}

// 辅助函数
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

function randomChoice(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function randomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function groupBy(array, key) {
  const result = {};
  for (const item of array) {
    const value = getNestedValue(item, key);
    result[value] = (result[value] || 0) + 1;
  }
  return result;
}

function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

// 导出Worker
export default {
  fetch: app.fetch

  // 定时任务功能暂时禁用，等部署成功后再启用
  // async scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
  //   console.log('定时任务触发:', new Date().toISOString());
  //   console.log('测试机器人调度器开发中...');
  // }
};
