/**
 * 问卷测试机器人 - 主入口
 */

import { Hono } from 'hono';

export interface Env {
  TEST_CONFIG: KVNamespace;
  TEST_RESULTS: KVNamespace;
  TEST_METRICS: KVNamespace;
  TEST_DATA_POOL: KVNamespace;
  TEST_DB: D1Database;
  TARGET_API_BASE: string;
  TEST_MODE: string;
  MAX_CONCURRENT_REQUESTS: string;
  DEFAULT_TEST_INTENSITY: string;
}

const app = new Hono<{ Bindings: Env }>();

// 健康检查
app.get('/', (c) => {
  return c.json({
    service: '问卷测试机器人',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    environment: c.env.TEST_MODE,
    target: c.env.TARGET_API_BASE
  });
});

// 获取系统状态
app.get('/api/status', (c) => {
  return c.json({
    success: true,
    data: {
      status: 'stopped',
      message: '测试机器人已就绪，等待配置和启动',
      timestamp: new Date().toISOString()
    }
  });
});

// 启动测试
app.post('/api/start', async (c) => {
  return c.json({
    success: true,
    message: '测试启动功能开发中',
    timestamp: new Date().toISOString()
  });
});

// 停止测试
app.post('/api/stop', async (c) => {
  return c.json({
    success: true,
    message: '测试停止功能开发中',
    timestamp: new Date().toISOString()
  });
});

// 初始化数据池
app.post('/api/data/init-pools', async (c) => {
  return c.json({
    success: true,
    message: '数据池初始化功能开发中',
    timestamp: new Date().toISOString()
  });
});

export default {
  fetch: app.fetch,
  
  // Cron触发器 - 每分钟执行
  async scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
    console.log('定时任务触发:', new Date().toISOString());
    console.log('测试机器人调度器开发中...');
  }
};
