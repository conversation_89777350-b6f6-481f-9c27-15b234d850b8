{"$schema": "node_modules/wrangler/config-schema.json", "name": "college-employment-test-robot", "main": "src/index.ts", "account_id": "19ff11f47d9fadd0ed944c90ca274e24", "workers_dev": true, "compatibility_date": "2024-06-01", "kv_namespaces": [{"binding": "config", "id": "600591387fff40acb3af6035c0d900f6"}, {"binding": "data_pool", "id": "0c10003a2dee46808cb5b7cdb06d52dd"}, {"binding": "metrics", "id": "45310c05d53044858343e9c420cce99a"}, {"binding": "results", "id": "1a21e05fa4624d73b70d6ca175e76748"}], "d1_databases": [{"binding": "DB", "database_name": "test-robot-db", "database_id": "9c5a97d7-a780-42df-b902-52d42f2ab480"}], "vars": {"TARGET_API_BASE": "https://college-employment-survey.aibook2099.workers.dev", "TEST_MODE": "production_testing", "MAX_CONCURRENT_REQUESTS": "50", "DEFAULT_TEST_INTENSITY": "10", "ENVIRONMENT": "isolated_testing"}}